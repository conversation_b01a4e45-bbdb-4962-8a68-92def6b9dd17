<template>
  <ion-modal :is-open="isOpen" @didDismiss="closeModal">
    <ion-header>
      <ion-toolbar>
        <ion-title>{{ branch ? '編輯分會' : '創建分會' }}</ion-title>

        <ion-buttons slot="end">
          <ion-button @click="closeModal">取消</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <form @submit.prevent="handleSubmit" class="form-container">
        <div class="form-content">
          <ion-list>
            <ion-item>
              <ion-label position="stacked">分會名稱 *</ion-label>
              <ion-input
                v-model="formData.name"
                type="text"
                required
                placeholder="請輸入分會名稱"
              ></ion-input>
            </ion-item>

            <ion-item>
              <ion-label position="stacked">分會簡介</ion-label>
              <ion-textarea
                v-model="formData.description"
                placeholder="請輸入分會簡介"
                :rows="3"
              ></ion-textarea>
            </ion-item>

            <ion-item>
              <ion-label position="stacked">分會介紹</ion-label>
              <ion-textarea
                v-model="formData.introduction"
                placeholder="請輸入分會詳細介紹"
                :rows="4"
              ></ion-textarea>
            </ion-item>

            <ion-item>
              <ion-label position="stacked">分會理念</ion-label>
              <ion-textarea
                v-model="formData.philosophy"
                placeholder="請輸入分會理念"
                :rows="4"
              ></ion-textarea>
            </ion-item>

            <ion-item>
              <ion-label position="stacked">分會類別 *</ion-label>
              <ion-select
                v-model="formData.category_id"
                placeholder="請選擇分會類別"
                required
              >
                <ion-select-option
                  v-for="category in categories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.title }}
                </ion-select-option>
              </ion-select>
            </ion-item>

            <ion-item>
              <ion-label position="stacked">地區 *</ion-label>
              <ion-select
                v-model="formData.district"
                placeholder="請選擇地區"
                required
              >
                <ion-select-option value="中西區">中西區</ion-select-option>
                <ion-select-option value="灣仔區">灣仔區</ion-select-option>
                <ion-select-option value="東區">東區</ion-select-option>
                <ion-select-option value="南區">南區</ion-select-option>
                <ion-select-option value="油尖旺區">油尖旺區</ion-select-option>
                <ion-select-option value="深水埗區">深水埗區</ion-select-option>
                <ion-select-option value="九龍城區">九龍城區</ion-select-option>
                <ion-select-option value="黃大仙區">黃大仙區</ion-select-option>
                <ion-select-option value="觀塘區">觀塘區</ion-select-option>
                <ion-select-option value="葵青區">葵青區</ion-select-option>
                <ion-select-option value="荃灣區">荃灣區</ion-select-option>
                <ion-select-option value="屯門區">屯門區</ion-select-option>
                <ion-select-option value="元朗區">元朗區</ion-select-option>
                <ion-select-option value="北區">北區</ion-select-option>
                <ion-select-option value="大埔區">大埔區</ion-select-option>
                <ion-select-option value="沙田區">沙田區</ion-select-option>
                <ion-select-option value="西貢區">西貢區</ion-select-option>
                <ion-select-option value="離島區">離島區</ion-select-option>
              </ion-select>
            </ion-item>

            <!-- Logo Upload -->
            <ion-item>
              <ion-label position="stacked">分會標誌</ion-label>
              <div class="image-upload-container">
                <div class="preview-container" v-if="logoPreview || formData.logo">
                  <img :src="logoPreview || formData.logo" alt="Logo preview" class="logo-preview" />
                  <ion-button fill="clear" color="danger" @click="removeLogo">
                    <ion-icon :icon="trashOutline"></ion-icon>
                  </ion-button>
                </div>
                <ion-button v-else expand="block" @click="takeLogo" fill="outline">
                  <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
                  選擇標誌
                </ion-button>
                <p class="upload-hint">建議上傳正方形圖片以獲得最佳顯示效果</p>
              </div>
            </ion-item>

            <!-- Banner Upload -->
            <ion-item>
              <ion-label position="stacked">分會橫幅</ion-label>
              <div class="image-upload-container">
                <div class="preview-container" v-if="bannerPreview || formData.banner">
                  <img :src="bannerPreview || formData.banner" alt="Banner preview" class="image-preview banner-preview" />
                  <ion-button fill="clear" color="danger" @click="removeBanner">
                    <ion-icon :icon="trashOutline"></ion-icon>
                  </ion-button>
                </div>
                <ion-button v-else expand="block" @click="takeBanner" fill="outline">
                  <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
                  選擇橫幅
                </ion-button>
                <p class="upload-hint">建議上傳橫向圖片作為分會橫幅</p>
              </div>
            </ion-item>
          </ion-list>

          <!-- Spacer to ensure content scrolls above the fixed button -->
          <div class="button-spacer"></div>
        </div>

        <!-- Fixed footer with submit button -->
        <ion-footer class="ion-no-border footer-container">
          <ion-toolbar>
            <ion-button
              type="submit"
              expand="block"
              :disabled="isSubmitting"
              class="submit-button"
            >
              <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
              <span v-else>{{ branch ? '更新' : '創建' }}</span>
            </ion-button>
          </ion-toolbar>
        </ion-footer>
      </form>
    </ion-content>
  </ion-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonItem,
  IonLabel,
  IonInput,
  IonTextarea,
  IonSelect,
  IonSelectOption,
  IonButton,
  IonButtons,
  IonIcon,
  IonSpinner,
  IonFooter,
} from '@ionic/vue';
import {
  cameraOutline,
  trashOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { uploadImages } from '@/lib/cloudflare';
import { usePhotoGallery, Photo } from '@/composables/usePhotoGallery';
import { utils } from '@/composables/utils';

const { presentToast } = utils();

const props = defineProps<{
  isOpen: boolean;
  branch?: {
    id: string;
    name: string;
    description?: string;
    introduction?: string;
    philosophy?: string;
    logo?: string;
    banner?: string;
    category_id?: string;
    district?: string;
  } | null;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'created', branch: any): void;
  (e: 'updated', branch: any): void;
}>();

const authStore = useAuthStore();
const isSubmitting = ref(false);
const categories = ref<any[]>([]);

// Initialize photo gallery
const { takePhoto } = usePhotoGallery();

// Image handling variables
const logoPreview = ref<string | null>(null);
const bannerPreview = ref<string | null>(null);
const logoPhoto = ref<Photo | null>(null);
const bannerPhoto = ref<Photo | null>(null);

const formData = ref({
  name: '',
  description: '',
  introduction: '',
  philosophy: '',
  logo: '',
  banner: '',
  category_id: '',
  district: '',
});

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    introduction: '',
    philosophy: '',
    logo: '',
    banner: '',
    category_id: '',
    district: '',
  };
  logoPreview.value = null;
  bannerPreview.value = null;
  logoPhoto.value = null;
  bannerPhoto.value = null;
};

const initializeForm = (branch: any) => {
  if (branch) {
    formData.value = {
      name: branch.name,
      description: branch.description || '',
      introduction: branch.introduction || '',
      philosophy: branch.philosophy || '',
      logo: branch.logo || '',
      banner: branch.banner || '',
      category_id: branch.category_id || '',
      district: branch.district || '',
    };
  } else {
    resetForm();
  }
};

watch(() => props.branch, (newBranch) => {
  initializeForm(newBranch);
}, { immediate: true });

const closeModal = () => {
  emit('close');
};

const takeLogo = async () => {
  try {
    const photo = await takePhoto();
    if (photo) {
      logoPhoto.value = photo;
      logoPreview.value = photo.base64Data || null;
    }
  } catch (error) {
    console.error('Error taking logo photo:', error);
    presentToast('無法獲取相片，請稍後再試', 2000);
  }
};

const takeBanner = async () => {
  try {
    const photo = await takePhoto();
    if (photo) {
      bannerPhoto.value = photo;
      bannerPreview.value = photo.base64Data || null;
    }
  } catch (error) {
    console.error('Error taking banner photo:', error);
    presentToast('無法獲取相片，請稍後再試', 2000);
  }
};

const removeLogo = () => {
  logoPhoto.value = null;
  logoPreview.value = null;
  formData.value.logo = '';
};

const removeBanner = () => {
  bannerPhoto.value = null;
  bannerPreview.value = null;
  formData.value.banner = '';
};

const loadCategories = async () => {
  try {
    const { data, error } = await supabase
      .from('branch_categories')
      .select('*')
      .order('title', { ascending: true });

    if (error) throw error;
    categories.value = data || [];
  } catch (error) {
    console.error('Error loading branch categories:', error);
    presentToast('載入分會類別失敗', 2000);
  }
};

onMounted(() => {
  loadCategories();
});

const handleSubmit = async () => {
  if (!formData.value.name || !formData.value.category_id || !formData.value.district) {
    presentToast('請填寫必填欄位', 2000);
    return;
  }

  try {
    isSubmitting.value = true;

    // Upload logo and banner if provided
    let logoUrl = formData.value.logo;
    let bannerUrl = formData.value.banner;

    // Prepare images for upload
    const imageInputs = [];

    if (logoPhoto.value && logoPhoto.value.base64Data) {
      imageInputs.push({
        base64Data: logoPhoto.value.base64Data,
        filename: logoPhoto.value.filepath || `logo_${new Date().getTime()}.jpg`,
        mimeType: logoPhoto.value.mimeType || 'image/jpeg'
      });
    }

    if (bannerPhoto.value && bannerPhoto.value.base64Data) {
      imageInputs.push({
        base64Data: bannerPhoto.value.base64Data,
        filename: bannerPhoto.value.filepath || `banner_${new Date().getTime()}.jpg`,
        mimeType: bannerPhoto.value.mimeType || 'image/jpeg'
      });
    }

    // Upload images if any
    if (imageInputs.length > 0) {
      try {
        const uploadedUrls = await uploadImages(imageInputs);

        // Assign URLs to the appropriate variables
        if (logoPhoto.value && logoPhoto.value.base64Data) {
          logoUrl = uploadedUrls.shift() || '';
        }

        if (bannerPhoto.value && bannerPhoto.value.base64Data) {
          bannerUrl = uploadedUrls.shift() || '';
        }
      } catch (error) {
        console.error('Error uploading images:', error);
        presentToast('圖片上傳失敗，請稍後再試', 2000);
        throw error;
      }
    }

    if (props.branch) {
      // Update existing branch
      const { data: updatedBranch, error } = await supabase
        .from('branches')
        .update({
          name: formData.value.name,
          description: formData.value.description,
          introduction: formData.value.introduction,
          philosophy: formData.value.philosophy,
          logo: logoUrl,
          banner: bannerUrl,
          category_id: formData.value.category_id,
          district: formData.value.district,
        })
        .eq('id', props.branch.id)
        .select()
        .single();

      if (error) throw error;
      emit('updated', updatedBranch);
    } else {
      // Create new branch
      const { data: newBranch, error } = await supabase
        .from('branches')
        .insert({
          name: formData.value.name,
          description: formData.value.description,
          introduction: formData.value.introduction,
          philosophy: formData.value.philosophy,
          logo: logoUrl,
          banner: bannerUrl,
          category_id: formData.value.category_id,
          district: formData.value.district,
          owner_id: authStore.currentUser.id,
        })
        .select()
        .single();

      if (error) throw error;
      emit('created', newBranch);
    }

    closeModal();
  } catch (error: any) {
    console.error('Branch operation failed:', error);
    presentToast(error.message, 2000);
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
.image-upload-container {
  width: 100%;
  padding: 1rem 0;
}

.preview-container {
  position: relative;
  display: inline-block;
}

.preview-container ion-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  margin: 0;
}

.image-preview {
  border-radius: 8px;
  border: 1px solid var(--ion-color-medium);
}

.logo-preview {
  width: 120px;
  height: 120px;
  object-fit: cover;
}

.banner-preview {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

/* Fixed footer styles */
.form-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.form-content {
  flex: 1;
  overflow-y: auto;
}

.button-spacer {
  height: 70px; /* Match the height of the footer */
}

.footer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--ion-background-color);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.submit-button {
  margin: 8px 16px;
}
</style>
