<template>
  <ion-card
    mode="ios"
    :class="['branch-card', { featured: branch.is_featured }]"
    button
    @click="goToBranchDetail"
  >
    <div class="banner-image-container">
      <img
        :src="branch.banner || 'https://images.unsplash.com/photo-1517048676732-d65bc937f952'"
        :alt="branch.name"
        loading="lazy"
        decoding="async"
      />
      <div class="loading-placeholder"></div>
      <div v-if="branch.is_featured" class="featured-badge">
        <ion-icon :icon="star"></ion-icon>
        精選分會
      </div>
    </div>
    <ion-card-header>
      <div class="branch-info">
        <img
          class="square-logo-small"
          :src="branch.logo || 'https://images.unsplash.com/photo-1522071820081-009f0129c71c'"
          :alt="branch.name"
          loading="lazy"
          decoding="async"
        />
        <div class="branch-details">
          <ion-card-title>{{ branch.name }}</ion-card-title>
          <div class="branch-meta">
            <span class="member-count">
              <ion-icon :icon="peopleOutline"></ion-icon>
              {{ branch.member_count || 0 }} 位成員
            </span>
            <span class="owner-name">
              <ion-icon :icon="personOutline"></ion-icon>
              {{ branch.owner?.full_name || '未知' }}
            </span>
          </div>
          <div class="branch-meta">
            <span class="district">{{ branch.district }}</span>
            <span class="created-time">
              <ion-icon :icon="timeOutline"></ion-icon>
              {{ formatTimeAgo(branch.created_at) }}
            </span>
          </div>
          <div class="branch-activity-level">
            <span :class="getActivityLevelClass(branch.activity_level)">
              {{ getActivityLevelText(branch.activity_level) }}
            </span>
          </div>
        </div>
      </div>
    </ion-card-header>
    <ion-card-content v-if="showJoinButton">
      <ion-button
        v-if="authStore.isAuthenticated && !isMember && !hasPendingApplication"
        size="small"
        expand="block"
        @click.stop.prevent="applyToBranch(branch.id)"
      >
        <ion-icon :icon="personAddOutline" slot="start"></ion-icon>
        申請加入分會
      </ion-button>
      <ion-button
        v-else-if="authStore.isAuthenticated && !isMember && hasPendingApplication"
        size="small"
        expand="block"
        color="medium"
        disabled
      >
        <ion-icon :icon="timeOutline" slot="start"></ion-icon>
        申請審核中
      </ion-button>
    </ion-card-content>
  </ion-card>

  <ion-toast
    :is-open="!!toastMessage"
    :message="toastMessage"
    :duration="3000"
    @didDismiss="toastMessage = ''"
  ></ion-toast>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonIcon,
  IonButton,
  IonToast,
  alertController,
  loadingController,
} from '@ionic/vue';
import {
  peopleOutline,
  personOutline,
  timeOutline,
  personAddOutline,
  star,
} from 'ionicons/icons';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import { supabase } from '@/lib/supabase';
import { useRouter } from 'vue-router';
import { useAlert } from '@/composables/useAlert';

const props = defineProps({
  branch: {
    type: Object,
    required: true
  },
  isMember: {
    type: Boolean,
    default: false
  },
  showJoinButton: {
    type: Boolean,
    default: true
  }
});

const authStore = useAuthStore();
const userStore = useUserStore();
const router = useRouter();
const toastMessage = ref('');
const { presentPrompt } = useAlert();

// Navigate to branch detail page
const goToBranchDetail = () => {
  router.push(`/branches/${props.branch.id}`);
};

// Check if user has a pending application for this branch
const hasPendingApplication = computed(() => {
  if (!authStore.isAuthenticated || !props.branch?.id) {
    return false;
  }
  return userStore.hasPendingApplication(props.branch.id);
});

// Function to prompt for application reason and submit application
const promptForApplicationReason = async (branchId: string) => {
  try {
    const { data, role } = await presentPrompt({
      header: '申請理由',
      message: '請簡單描述您申請加入的原因',
      inputs: [
        {
          name: 'reason',
          type: 'textarea',
          placeholder: '申請理由...'
        }
      ],
      buttons: [
        {
          text: '取消',
          role: 'cancel'
        },
        {
          text: '提交',
          role: 'confirm'
        }
      ]
    });

    if (role === 'confirm' && data?.values?.reason) {
      await submitApplication(branchId, data.values.reason);
    }
  } catch (error) {
    console.error('Error showing reason prompt:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  }
};

// Function to submit the application to the database
const submitApplication = async (branchId: string, reason: string) => {
  try {
    const loading = await loadingController.create({});
    await loading.present();

    const newApplication = {
      branch_id: branchId,
      user_id: authStore.currentUser.id,
      message: reason,
      status: 'pending',
      created_at: new Date().toISOString()
    };

    // Submit application to database
    const { data, error } = await supabase
      .from('branch_member_applications')
      .insert(newApplication)
      .select()
      .single();

    if (error) throw error;

    // Add the application to the user store
    if (data) {
      userStore.addBranchApplication(data);
    }

    // Show success message
    toastMessage.value = '申請已提交，請等待審核';
  } catch (error) {
    console.error('Error submitting application:', error);
    toastMessage.value = '申請提交失敗，請稍後再試';
  } finally {
    loadingController.dismiss();
  }
};

// Apply to join a branch
const applyToBranch = async (branchId: string) => {
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入';
    return;
  }

  try {
    // Check if already applied using the computed property
    if (hasPendingApplication.value) {
      toastMessage.value = '您已經申請加入此分會，請等待審核';
      return;
    }

    // Show prompt for application reason
    await promptForApplicationReason(branchId);
  } catch (error) {
    console.error('Error in application process:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  }
};

// Format time as "X time ago"
const formatTimeAgo = (dateString: string) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();

  // Convert to appropriate time unit
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffMonth / 12);

  if (diffYear > 0) {
    return `${diffYear} 年前`;
  } else if (diffMonth > 0) {
    return `${diffMonth} 個月前`;
  } else if (diffDay > 0) {
    return `${diffDay} 天前`;
  } else if (diffHour > 0) {
    return `${diffHour} 小時前`;
  } else if (diffMin > 0) {
    return `${diffMin} 分鐘前`;
  } else {
    return '剛剛';
  }
};

// Get activity level text
const getActivityLevelText = (level: number) => {
  switch (level) {
    case 0:
      return '活躍度: 低';
    case 1:
      return '活躍度: 中';
    case 2:
      return '活躍度: 高';
    default:
      return '活躍度: 未知';
  }
};

// Get activity level CSS class
const getActivityLevelClass = (level: number) => {
  switch (level) {
    case 0:
      return 'activity-low';
    case 1:
      return 'activity-medium';
    case 2:
      return 'activity-high';
    default:
      return 'activity-unknown';
  }
};

// No need to check application status when component is mounted
// as it's now handled by the computed property
</script>

<style scoped>
.branch-card {
  margin: 0;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

.branch-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* Banner styling now handled by global .banner-image-container class */

@media (hover: hover) {
  .branch-card:hover .banner-image-container img {
    transform: scale(1.05);
  }
}

.branch-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

/* Branch logo styling now handled by global .square-logo-small class */

.branch-details {
  flex: 1;
}

.branch-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  margin-bottom: 4px;
}

.member-count,
.district,
.owner-name,
.created-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

.branch-activity-level {
  margin-top: 6px;
}

.branch-activity-level span {
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.activity-low {
  background-color: rgba(var(--ion-color-danger-rgb), 0.15);
  color: var(--ion-color-danger);
}

.activity-medium {
  background-color: rgba(var(--ion-color-warning-rgb), 0.15);
  color: var(--ion-color-warning-shade);
}

.activity-high {
  background-color: rgba(var(--ion-color-success-rgb), 0.15);
  color: var(--ion-color-success);
}

.activity-unknown {
  background-color: rgba(var(--ion-color-medium-rgb), 0.15);
  color: var(--ion-color-medium);
}

.featured-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: var(--ion-color-primary);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 10;
}

ion-card-header {
  padding: 12px 16px;
}

ion-card-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 768px) {
  .branch-card:hover {
    transform: none;
  }

  .branch-card:hover .branch-banner img {
    transform: none;
  }
}
</style>
